<!-- Drawer Overlay -->
<div
  *ngIf="isOpen && showOverlay"
  [class]="overlayClasses()"
  [style.z-index]="zIndex - 1"
  (click)="onOverlayClick()"
  role="presentation"
  aria-hidden="true">
</div>

<!-- Drawer Container -->
<div
  *ngIf="isOpen"
  [class]="drawerClasses()"
  [ngStyle]="drawerStyles()"
  (click)="onDrawerClick($event)"
  role="dialog"
  [attr.aria-modal]="true"
  [attr.aria-labelledby]="title ? 'drawer-title' : null"
  [attr.aria-describedby]="subtitle ? 'drawer-subtitle' : null">

  <!-- Animation Wrapper -->
  <div class="ava-drawer__animation-wrapper"
       [class.ava-drawer--animated]="animate"
       [class.ava-drawer--no-animate]="!animate">

    <!-- Drawer Content -->
    <div class="ava-drawer__content">

    <!-- Header Section -->
    <div *ngIf="showHeader" class="ava-drawer__header">
      <div class="ava-drawer__header-content">
        <!-- Title and Subtitle -->
        <div *ngIf="title || subtitle" class="ava-drawer__title-section">
          <h2 *ngIf="title" id="drawer-title" class="ava-drawer__title">
            {{ title }}
          </h2>
          <p *ngIf="subtitle" id="drawer-subtitle" class="ava-drawer__subtitle">
            {{ subtitle }}
          </p>
        </div>

        <!-- Custom Header Content -->
        <div class="ava-drawer__header-slot">
          <ng-content select="[slot=header]"></ng-content>
        </div>
      </div>

      <!-- Close Button -->
      <div *ngIf="showCloseButton" class="ava-drawer__close-section">
        <ava-button
          [iconName]="closeIcon"
          iconPosition="only"
          variant="secondary"
          size="small"
          [pill]="true"
          (click)="onCloseClick()"
          [attr.aria-label]="'Close drawer'"
          >
        </ava-button>
      </div>
    </div>

    <!-- Main Content Section -->
    <div class="ava-drawer__body">
      <ng-content></ng-content>
    </div>

    <!-- Footer Section -->
    <div *ngIf="showFooter" class="ava-drawer__footer">
      <ng-content select="[slot=footer]"></ng-content>
    </div>

  </div>

  </div> <!-- Close Animation Wrapper -->

  <!-- Resize Handle (if resizable) -->
  <div *ngIf="resizable" class="ava-drawer__resize-handle"
       [class]="'ava-drawer__resize-handle--' + position">
  </div>

</div>
