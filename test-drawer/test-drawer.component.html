<div class="test-drawer-container">
  <!-- Trigger <PERSON>ton -->
  <div class="trigger-section">
    <h1>Drawer Component Test</h1>
    <p>Click the button below to open the drawer with the exact design from the provided image.</p>

    <ava-button
      label="Open Component Card"
      variant="primary"
      size="large"
      (userClick)="openDrawer()">
    </ava-button>
  </div>

  <!-- Drawer Component -->
  <ava-drawer
    [isOpen]="isDrawerOpen"
    position="right"
    size="large"
    [showOverlay]="true"
    [closeOnOverlayClick]="true"
    [closeOnEscape]="true"
    [showCloseButton]="false"
    [showHeader]="false"
    [animate]="true"
    width="500px"
    height="100vh"
    (opened)="onDrawerOpened()"
    (closed)="onDrawerClosed()">

    <!-- Component Card Content -->
    <div class="component-card">
      <!-- Header with close button -->
      <div class="header">
        <h1 class="title">{{ componentData.title }}</h1>
        <ava-icon
          class="close-btn"
          iconName="x"
          [iconSize]="24"
          iconColor="#6b7280"
          [cursor]="true"
          (userClick)="closeDrawer()"
          aria-label="Close">
        </ava-icon>
      </div>

      <!-- Subtitle -->
      <p class="subtitle">{{ componentData.subtitle }}</p>

      <!-- Add to list button -->
      <ava-button
        class="add-to-list-btn"
        variant="secondary"
        size="medium"
        iconName="Plus"
        iconPosition="left"
        label="Add to my list"
        [outlined]="true"
        [pill]="true"
        (userClick)="onAddToList()">
      </ava-button>

      <!-- Tags -->
      <div class="tags">
        <span
          *ngFor="let tag of componentData.tags"
          class="tag"
          [class.accuracy-tag]="tag.type === 'accuracy'">
          {{ tag.label }}
        </span>
      </div>

      <!-- Agent ranking -->
      <div class="agent-ranking">
        <span class="ranking-text">{{ componentData.ranking }}</span>
      </div>

      <!-- Stats grid -->
      <div class="stats-grid">
        <div *ngFor="let stat of componentData.stats" class="stat-item">
          <div class="stat-label">{{ stat.label }}</div>

          <!-- Icon for Category and Developed by -->
          <div class="stat-icon" *ngIf="stat.icon && stat.icon !== 'Star'">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <ng-container [ngSwitch]="stat.icon">
                <!-- Code icon for Category -->
                <g *ngSwitchCase="'Code'">
                  <polyline points="16 18 22 12 16 6"></polyline>
                  <polyline points="8 6 2 12 8 18"></polyline>
                </g>
                <!-- User icon for Developed by -->
                <g *ngSwitchCase="'User'">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </g>
              </ng-container>
            </svg>
          </div>

          <!-- Value for Relevancy -->
          <div *ngIf="stat.value && stat.icon !== 'Star'" class="stat-value">{{ stat.value }}</div>

          <!-- Rating with star -->
          <div *ngIf="stat.value && stat.icon === 'Star'" class="stat-rating">
            <span class="rating-number">{{ stat.value }}</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="#fbbf24">
              <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
            </svg>
          </div>

          <div class="stat-sublabel">{{ stat.sublabel }}</div>
        </div>
      </div>

      <!-- What it's for section -->
      <div class="description-section">
        <h2 class="section-title">{{ componentData.description.title }}</h2>
        <p class="description-text">{{ componentData.description.content }}</p>
      </div>

      <!-- Go to Playground button -->
      <ava-button
        class="playground-btn"
        label="Go to Playground"
        variant="primary"
        size="large"
        width="100%"
        (userClick)="onGoToPlayground()">
      </ava-button>

      <!-- Drawer Features Demo Section -->
      <div class="drawer-features-section">
        <h2 class="features-title">Drawer Component Features</h2>

        <!-- Position Demo -->
        <div class="feature-group">
          <h3 class="feature-subtitle">Position Options</h3>
          <div class="feature-buttons">
            <ava-button
              label="Left Drawer"
              variant="secondary"
              size="small"
              (userClick)="openPositionDrawer('left')">
            </ava-button>
            <ava-button
              label="Top Drawer"
              variant="secondary"
              size="small"
              (userClick)="openPositionDrawer('top')">
            </ava-button>
            <ava-button
              label="Bottom Drawer"
              variant="secondary"
              size="small"
              (userClick)="openPositionDrawer('bottom')">
            </ava-button>
          </div>
        </div>

        <!-- Size Demo -->
        <div class="feature-group">
          <h3 class="feature-subtitle">Size Variants</h3>
          <div class="feature-buttons">
            <ava-button
              label="Small"
              variant="secondary"
              size="small"
              (userClick)="openSizeDrawer('small')">
            </ava-button>
            <ava-button
              label="Medium"
              variant="secondary"
              size="small"
              (userClick)="openSizeDrawer('medium')">
            </ava-button>
            <ava-button
              label="Large"
              variant="secondary"
              size="small"
              (userClick)="openSizeDrawer('large')">
            </ava-button>
            <ava-button
              label="Extra Large"
              variant="secondary"
              size="small"
              (userClick)="openSizeDrawer('extra-large')">
            </ava-button>
          </div>
        </div>

        <!-- Behavior Demo -->
        <div class="feature-group">
          <h3 class="feature-subtitle">Behavior Options</h3>
          <div class="feature-buttons">
            <ava-button
              label="No Overlay"
              variant="secondary"
              size="small"
              (userClick)="openBehaviorDrawer('no-overlay')">
            </ava-button>
            <ava-button
              label="Persistent"
              variant="secondary"
              size="small"
              (userClick)="openBehaviorDrawer('persistent')">
            </ava-button>
            <ava-button
              label="No Animation"
              variant="secondary"
              size="small"
              (userClick)="openBehaviorDrawer('no-animation')">
            </ava-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Demo Drawers for Features -->
    <!-- Position Drawers -->
    <ava-drawer
      [isOpen]="demoDrawers.left"
      position="left"
      size="medium"
      title="Left Position Drawer"
      subtitle="This drawer slides in from the left"
      (closed)="closeDemoDrawer('left')">
      <div class="demo-content">
        <p>This drawer demonstrates the <strong>left position</strong> option.</p>
        <p>Perfect for navigation menus or secondary content.</p>
      </div>
    </ava-drawer>

    <ava-drawer
      [isOpen]="demoDrawers.top"
      position="top"
      size="medium"
      title="Top Position Drawer"
      subtitle="This drawer slides in from the top"
      (closed)="closeDemoDrawer('top')">
      <div class="demo-content">
        <p>This drawer demonstrates the <strong>top position</strong> option.</p>
        <p>Great for notifications or quick actions.</p>
      </div>
    </ava-drawer>

    <ava-drawer
      [isOpen]="demoDrawers.bottom"
      position="bottom"
      size="medium"
      title="Bottom Position Drawer"
      subtitle="This drawer slides in from the bottom"
      (closed)="closeDemoDrawer('bottom')">
      <div class="demo-content">
        <p>This drawer demonstrates the <strong>bottom position</strong> option.</p>
        <p>Ideal for mobile-friendly interfaces and action sheets.</p>
      </div>
    </ava-drawer>

    <!-- Size Drawers -->
    <ava-drawer
      [isOpen]="demoDrawers.sizeSmall"
      position="right"
      size="small"
      title="Small Size Drawer"
      subtitle="Compact drawer for minimal content"
      (closed)="closeDemoDrawer('sizeSmall')">
      <div class="demo-content">
        <p>This is a <strong>small</strong> drawer (240px width).</p>
        <p>Perfect for simple forms or quick actions.</p>
      </div>
    </ava-drawer>

    <ava-drawer
      [isOpen]="demoDrawers.sizeMedium"
      position="right"
      size="medium"
      title="Medium Size Drawer"
      subtitle="Standard drawer size for most content"
      (closed)="closeDemoDrawer('sizeMedium')">
      <div class="demo-content">
        <p>This is a <strong>medium</strong> drawer (300px width).</p>
        <p>The default size, suitable for most use cases.</p>
      </div>
    </ava-drawer>

    <ava-drawer
      [isOpen]="demoDrawers.sizeLarge"
      position="right"
      size="large"
      title="Large Size Drawer"
      subtitle="Spacious drawer for detailed content"
      (closed)="closeDemoDrawer('sizeLarge')">
      <div class="demo-content">
        <p>This is a <strong>large</strong> drawer (400px width).</p>
        <p>Great for detailed forms or rich content.</p>
      </div>
    </ava-drawer>

    <ava-drawer
      [isOpen]="demoDrawers.sizeExtraLarge"
      position="right"
      size="extra-large"
      title="Extra Large Size Drawer"
      subtitle="Maximum width for complex layouts"
      (closed)="closeDemoDrawer('sizeExtraLarge')">
      <div class="demo-content">
        <p>This is an <strong>extra-large</strong> drawer (500px width).</p>
        <p>Perfect for complex forms or detailed views.</p>
      </div>
    </ava-drawer>

    <!-- Behavior Drawers -->
    <ava-drawer
      [isOpen]="demoDrawers.noOverlay"
      position="right"
      size="medium"
      [showOverlay]="false"
      title="No Overlay Drawer"
      subtitle="Drawer without background overlay"
      (closed)="closeDemoDrawer('noOverlay')">
      <div class="demo-content">
        <p>This drawer has <strong>no overlay</strong>.</p>
        <p>The background content remains interactive.</p>
      </div>
    </ava-drawer>

    <ava-drawer
      [isOpen]="demoDrawers.persistent"
      position="right"
      size="medium"
      [persistent]="true"
      [closeOnOverlayClick]="false"
      [closeOnEscape]="false"
      title="Persistent Drawer"
      subtitle="Cannot be closed by clicking outside or pressing Escape"
      (closed)="closeDemoDrawer('persistent')">
      <div class="demo-content">
        <p>This is a <strong>persistent</strong> drawer.</p>
        <p>It can only be closed using the close button.</p>
        <ava-button
          label="Close Drawer"
          variant="primary"
          (userClick)="closeDemoDrawer('persistent')">
        </ava-button>
      </div>
    </ava-drawer>

    <ava-drawer
      [isOpen]="demoDrawers.noAnimation"
      position="right"
      size="medium"
      [animate]="false"
      title="No Animation Drawer"
      subtitle="Drawer without slide animations"
      (closed)="closeDemoDrawer('noAnimation')">
      <div class="demo-content">
        <p>This drawer has <strong>no animations</strong>.</p>
        <p>It appears instantly without slide effects.</p>
      </div>
    </ava-drawer>

  </ava-drawer>
</div>
