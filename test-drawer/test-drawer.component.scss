/* Test Drawer Component Styles - Matching the provided design exactly */

.test-drawer-container {
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

.trigger-section {
  text-align: center;
  max-width: 600px;

  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
  }
}

/* Component Card Styles - Exact match to the provided design */
.component-card {
  width: 100%;
  background: white;
  border-radius: 0;
  padding: 24px;
  box-shadow: none;
  position: relative;
  margin: 0;
  height: 100%;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
  flex: 1;
  padding-right: 16px;
}

.close-btn {
  padding: 4px;
  border-radius: 6px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #f3f4f6;
  }
}

.subtitle {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
  margin: 0 0 20px 0;
}

.add-to-list-btn {
  margin-bottom: 24px;

  ::ng-deep .ava-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: 2px solid #ec4899;
    color: #ec4899;
    padding: 8px 16px;
    border-radius: 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    width: auto;

    &:hover {
      background-color: #ec4899;
      color: white;
    }
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.tag {
  background-color: #f3f4f6;
  color: #374151;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;

  &.accuracy-tag {
    background-color: #dbeafe;
    color: #1e40af;
  }
}

.agent-ranking {
  margin-bottom: 24px;
}

.ranking-text {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 32px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  color: #9ca3af;
  font-size: 13px;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-icon {
  color: #6b7280;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;

  svg {
    width: 20px;
    height: 20px;
    stroke: #6b7280;
  }
}

.stat-value {
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-bottom: 4px;

  svg {
    width: 16px;
    height: 16px;
  }
}

.rating-number {
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.stat-sublabel {
  color: #9ca3af;
  font-size: 12px;
}

.description-section {
  background-color: #fce7f3;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
}

.section-title {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.description-text {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.playground-btn {
  ::ng-deep .ava-button {
    width: 100%;
    background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(236, 72, 153, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* Drawer Features Demo Section */
.drawer-features-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.features-title {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 24px 0;
}

.feature-group {
  margin-bottom: 24px;
}

.feature-subtitle {
  color: #374151;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.feature-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  ::ng-deep .ava-button {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    transition: all 0.2s;

    &:hover {
      background-color: #f9fafb;
      border-color: #9ca3af;
    }
  }
}

/* Demo content styling */
.demo-content {
  padding: 16px 0;

  p {
    margin: 0 0 12px 0;
    line-height: 1.5;
    color: #4b5563;

    strong {
      color: #1f2937;
      font-weight: 600;
    }
  }

  p:last-child {
    margin-bottom: 0;
  }
}
