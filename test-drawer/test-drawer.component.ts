import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DrawerComponent } from '../projects/play-comp-library/src/lib/components/drawer/drawer.component';
import { ButtonComponent } from '../projects/play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../projects/play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'test-drawer',
  standalone: true,
  imports: [CommonModule, DrawerComponent, ButtonComponent, IconComponent],
  templateUrl: './test-drawer.component.html',
  styleUrls: ['./test-drawer.component.scss']
})
export class TestDrawerComponent implements OnInit {
  isDrawerOpen = false;

  // Demo drawers state
  demoDrawers = {
    left: false,
    top: false,
    bottom: false,
    sizeSmall: false,
    sizeMedium: false,
    sizeLarge: false,
    sizeExtraLarge: false,
    noOverlay: false,
    persistent: false,
    noAnimation: false
  };

  // Component data matching the design
  componentData = {
    title: 'Create Angular Component',
    subtitle: 'Effortlessly convert Ruby code to Spring Boot with optimised migration',
    tags: [
      { label: 'Development', type: 'default' },
      { label: 'Front End', type: 'default' },
      { label: 'Framework', type: 'default' },
      { label: '98% Accuracy', type: 'accuracy' }
    ],
    ranking: '#4 in agents',
    stats: [
      {
        label: 'Category',
        icon: 'Code',
        sublabel: 'Type'
      },
      {
        label: 'Developed by',
        icon: 'User',
        sublabel: 'Name'
      },
      {
        label: 'Relevancy',
        value: '9.5/10',
        sublabel: 'Score'
      },
      {
        label: 'Rating',
        value: '4.5',
        icon: 'Star',
        sublabel: 'Out of 5'
      }
    ],
    description: {
      title: 'What it\'s for',
      content: 'A agent that converts Ruby code to Spring Boot can be highly beneficial for organisation\'s migrating from Ruby on Rails to Java Spring Boot. However, the effectiveness depends on several factors, including the complexity of the application, language differences, and the capabilities of the conversion agent.'
    }
  };

  ngOnInit(): void {
    // Component initialization
  }

  openDrawer(): void {
    this.isDrawerOpen = true;
  }

  closeDrawer(): void {
    this.isDrawerOpen = false;
  }

  onDrawerOpened(): void {
    console.log('Drawer opened');
  }

  onDrawerClosed(): void {
    console.log('Drawer closed');
    this.isDrawerOpen = false;
  }

  onAddToList(): void {
    console.log('Add to list clicked');
  }

  onGoToPlayground(): void {
    console.log('Go to Playground clicked');
  }

  // Demo drawer methods
  openPositionDrawer(position: string): void {
    this.demoDrawers[position as keyof typeof this.demoDrawers] = true;
  }

  openSizeDrawer(size: string): void {
    const sizeKey = `size${size.charAt(0).toUpperCase() + size.slice(1).replace('-', '')}` as keyof typeof this.demoDrawers;
    this.demoDrawers[sizeKey] = true;
  }

  openBehaviorDrawer(behavior: string): void {
    const behaviorKey = behavior.replace('-', '').charAt(0).toLowerCase() + behavior.replace('-', '').slice(1);
    this.demoDrawers[behaviorKey as keyof typeof this.demoDrawers] = true;
  }

  closeDemoDrawer(drawerKey: string): void {
    this.demoDrawers[drawerKey as keyof typeof this.demoDrawers] = false;
  }
}
